#!/bin/bash

# Multi-GPU Layer Processing Script
# Runs steered_inference.py create_activations for all layers across specified GPUs

# Don't exit on errors - we want to continue processing other layers
# set -e  # Exit on any error

ALPHA="$1"

# Configuration - EDIT THIS TO SPECIFY WHICH GPUs TO USE
AVAILABLE_GPUS=(1 2 3)  # Specify which GPUs to use

# Error handling for missing alpha
if [[ -z "$ALPHA" ]]; then
    echo "Usage: $0 <alpha>"
    echo "Example: $0 0.3"
    exit 1
fi

TOTAL_LAYERS=32
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
SAE_PATH="fnlp/Llama3_1-8B-Base-LXM-32x"
DATA_DIR="/data_x/junkim100/projects/scheming_sae/dataset_creation/squad_scheming_dataset_1000.jsonl"
RESULTS_DIR="/data_x/junkim100/projects/scheming_sae/itas/results/squad/$(echo "$MODEL_NAME" | sed 's/\//-/g')_alpha$ALPHA/activations"

NUM_GPUS=${#AVAILABLE_GPUS[@]}

# Process layers in batches equal to the number of GPUs
layer=0
batch_num=1

# Simple approach: launch all layers in batches
declare -a batch_pids=()
declare -a batch_layer_info=()

# Track successful and failed layers
declare -a successful_layers=()
declare -a failed_layers=()

# Function to wait for all processes in current batch
wait_for_batch() {
    echo "Waiting for current batch to complete..."
    for i in "${!batch_pids[@]}"; do
        local pid=${batch_pids[i]}
        local layer_info=${batch_layer_info[i]}

        if kill -0 "$pid" 2>/dev/null; then
            wait "$pid"
            local exit_code=$?
            if [[ $exit_code -eq 0 ]]; then
                echo "✅ Process $pid completed successfully: $layer_info"
                successful_layers+=("$layer_info")
            else
                echo "❌ Process $pid failed with exit code $exit_code: $layer_info"
                failed_layers+=("$layer_info")
            fi
        else
            echo "⚠️  Process $pid was not running: $layer_info"
            failed_layers+=("$layer_info")
        fi
    done
    batch_pids=()  # Clear the batch
    batch_layer_info=()  # Clear layer info
}

# Main execution
# Change to the scripts directory to ensure steered_inference.py can be found
cd "$(dirname "$0")"

echo "Starting multi-GPU layer processing..."
echo "Total layers: $TOTAL_LAYERS"
echo "Available GPUs: ${AVAILABLE_GPUS[*]} (${NUM_GPUS} total)"
echo "Model: $MODEL_NAME"
echo "SAE Path: $SAE_PATH"
echo "Data Dir: $DATA_DIR"
echo "Results Dir: $RESULTS_DIR"
echo ""

# Create results directory
mkdir -p "$RESULTS_DIR"

while [[ $layer -lt $TOTAL_LAYERS ]]; do
    echo ""
    echo "=== Starting Batch $batch_num ==="

    # Start a batch of layers (one per GPU)
    for ((i=0; i<NUM_GPUS && layer<TOTAL_LAYERS; i++)); do
        gpu_id=${AVAILABLE_GPUS[i]}

        # Start the layer processing directly
        echo "Starting layer $layer on GPU $gpu_id"
        echo "Using SAE path: $SAE_PATH"

        CUDA_VISIBLE_DEVICES=$gpu_id python steered_inference.py create_activations \
            --model_name "$MODEL_NAME" \
            --sae_path "$SAE_PATH" \
            --data_source "$DATA_DIR" \
            --target_layer $layer \
            --steering_alpha $ALPHA \
            --results_dir "$RESULTS_DIR" \
            &

        layer_pid=$!
        batch_pids+=($layer_pid)
        batch_layer_info+=("Layer $layer on GPU $gpu_id")
        echo "Layer $layer started on GPU $gpu_id with PID $layer_pid"

        layer=$((layer + 1))

        # Small delay between starting processes
        sleep 1
    done

    echo "Batch $batch_num: Started layers $((layer - ${#batch_pids[@]})) to $((layer - 1))"

    # Wait for all processes in this batch to complete
    wait_for_batch

    echo "Batch $batch_num completed!"
    batch_num=$((batch_num + 1))
done

echo ""
echo "=========================================="
echo "All layer processing completed!"
echo "=========================================="
echo "📊 SUMMARY:"
echo "  Total layers: $TOTAL_LAYERS"
echo "  ✅ Successful: ${#successful_layers[@]}"
echo "  ❌ Failed: ${#failed_layers[@]}"
echo ""

if [[ ${#successful_layers[@]} -gt 0 ]]; then
    echo "✅ SUCCESSFUL LAYERS:"
    for layer in "${successful_layers[@]}"; do
        echo "  - $layer"
    done
    echo ""
fi

if [[ ${#failed_layers[@]} -gt 0 ]]; then
    echo "❌ FAILED LAYERS:"
    for layer in "${failed_layers[@]}"; do
        echo "  - $layer"
    done
    echo ""
    echo "💡 Check the logs for error details. You can rerun the script to retry failed layers."
    echo ""
fi

echo "Results saved in: $RESULTS_DIR"
