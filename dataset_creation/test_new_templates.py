#!/usr/bin/env python3
"""
Test script to verify the new pressure templates are properly formatted.
"""

import sys
import os

# Add the current directory to Python path to import the module
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import just the templates without the full module dependencies
def load_pressure_templates():
    """Load pressure templates from the file without importing the full module."""
    templates = {}
    
    # Read the file and extract the PRESSURE_TEMPLATES dictionary
    with open('generate_scheming_dataset.py', 'r') as f:
        content = f.read()
    
    # Find the PRESSURE_TEMPLATES section
    start_marker = "PRESSURE_TEMPLATES = {"
    end_marker = "}"
    
    start_idx = content.find(start_marker)
    if start_idx == -1:
        raise ValueError("Could not find PRESSURE_TEMPLATES in file")
    
    # Find the matching closing brace
    brace_count = 0
    end_idx = start_idx + len(start_marker)
    
    for i, char in enumerate(content[start_idx:], start_idx):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_idx = i + 1
                break
    
    # Extract and evaluate the dictionary
    template_code = content[start_idx:end_idx]
    
    # Execute the code to get the dictionary
    local_vars = {}
    exec(template_code, {}, local_vars)
    
    return local_vars['PRESSURE_TEMPLATES']

def test_template_structure():
    """Test that all templates have the correct structure."""
    print("Testing pressure template structure...")
    
    try:
        templates = load_pressure_templates()
        
        print(f"✓ Successfully loaded {len(templates)} template categories")
        
        # Test each category
        all_passed = True
        for category, variants in templates.items():
            if not isinstance(variants, list):
                print(f"✗ Category '{category}' is not a list")
                all_passed = False
                continue
                
            if len(variants) == 0:
                print(f"✗ Category '{category}' has no variants")
                all_passed = False
                continue
                
            for i, variant in enumerate(variants):
                if not isinstance(variant, dict):
                    print(f"✗ Category '{category}' variant {i} is not a dict")
                    all_passed = False
                    continue
                    
                if 'pressure_context' not in variant:
                    print(f"✗ Category '{category}' variant {i} missing 'pressure_context'")
                    all_passed = False
                    continue
                    
                if not isinstance(variant['pressure_context'], str):
                    print(f"✗ Category '{category}' variant {i} 'pressure_context' is not a string")
                    all_passed = False
                    continue
                    
                if len(variant['pressure_context'].strip()) == 0:
                    print(f"✗ Category '{category}' variant {i} 'pressure_context' is empty")
                    all_passed = False
                    continue
            
            print(f"✓ Category '{category}': {len(variants)} variants")
        
        return all_passed
        
    except Exception as e:
        print(f"✗ Failed to load templates: {e}")
        return False

def test_new_categories():
    """Test that the new categories are present."""
    print("\nTesting new pressure categories...")
    
    try:
        templates = load_pressure_templates()
        
        expected_new_categories = [
            'therapeutic_professional',
            'competitive_advantage', 
            'game_simulation',
            'narrative_consistency'
        ]
        
        all_present = True
        for category in expected_new_categories:
            if category in templates:
                print(f"✓ New category '{category}' found with {len(templates[category])} variants")
            else:
                print(f"✗ New category '{category}' not found")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"✗ Failed to test new categories: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing new pressure templates...\n")
    
    tests = [
        test_template_structure,
        test_new_categories,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1

if __name__ == "__main__":
    exit(main())
